# Phase 1 UI Features - ConnectPro Frontend Analysis

This document extracts the key UI features from the existing ConnectPro frontend that should be implemented in Phase 1 of ConnectPro UI (web, android and ios). The analysis focuses on authentication, user management, group management, dashboard, and profile features with white-labeling capabilities.

System must be a MOBILE FIRST user interface and user experience.

## 1. Authentication System (OTP-Based, No Passwords)

### 1.1 Email OTP Authentication

- **Magic Link Flow**: Send secure login links via email instead of password authentication
- **Biometric Authentication**: Mobile-specific fingerprint/face ID support with secure storage
- **Session Management**: "Remember Me" functionality and session persistence

### 1.2 Authentication UI Components

- **SignIn Page** (`pages/SignIn.tsx`): Clean login form with OTP support
- **SignUp Form** (`auth/SignUpForm.tsx`): User registration with name and email
- **Forgot Password** (`pages/ForgotPassword.tsx`): OTP-based reset with rate limiting
- **Auth Layout** (`components/AuthLayout.tsx`): Consistent styling for auth pages
- **Biometric Setup**: Automated setup prompts for mobile users
- **Language Switcher**: Multi-language support (EN/PT-BR)

### 1.3 Key Features for Laravel Implementation

```php
// OTP Email Authentication
- Mail integration with Mailpit for local development
- Rate limiting for login attempts and email sends
- Secure token generation and validation
- Session management without passwords
- Biometric token storage (mobile apps)
```

## 2. Super Admin Panel

### 2.1 Super Admin Dashboard (`pages/SuperAdminPage.tsx`)

- **User Management Tab**:

  - Create new users (name + email only)
  - Search and filter users by name/email
  - User impersonation for support
  - Delete user accounts with confirmation
  - View user roles and group memberships

- **Group Management Tab**:

  - Create new groups with auto-generated slugs
  - Assign admin to groups during creation
  - View group statistics (member count)
  - Navigate to individual group dashboards

- **Email Templates Tab**:
  - Preview and manage system email templates
  - Customizable content for different email types

### 2.2 User Management Features

- **User Creation**: Simple form with name and email (no password required)
- **User Search**: Real-time search across name and email fields
- **Role Management**: Assign users to groups with specific roles
- **Impersonation**: Admins can impersonate users for support
- **Audit Trails**: Track admin actions and user management

### 2.3 Group Creation Workflow

```typescript
// Group creation with admin assignment
interface GroupCreation {
  name: string;
  slug: string; // auto-generated from name
  adminEmail: string; // existing user email
  reviewFrequency: { value: number; unit: "weeks" | "months" };
  defaultMeetingDuration: number; // minutes
}
```

## 3. Dashboard Features

### 3.1 Main Dashboard (`pages/Dashboard.tsx`)

- **Dynamic Greeting**: Personalized time-based greetings
- **Group Context**: Shows current group information and branding
- **Meeting Integration**: Upcoming meeting card with RSVP functionality
- **Statistics Cards**:
  - Connection stats (meeting interactions)
  - Event statistics (upcoming meetings count)
  - Member metrics with growth rates
  - Quick action cards

### 3.2 Group Dashboard Components

- **Group Information** (`groups/GroupInformation.tsx`): Group details and branding
- **Member List** (`groups/GroupMemberList.tsx`): Paginated member management
- **Statistics Integration**: Real-time metrics and analytics
- **Meeting Management**: Quick meeting creation and attendance

### 3.3 Dashboard Data Structure

```typescript
interface DashboardData {
  connectionStats: { total: number; growthPercent: number };
  eventStats: { total: number; nextEventDays: number };
  memberMetrics: {
    totalMembers: number;
    newMembers: number;
    activeMembers: number;
    growthRate: number;
    engagementScore: number;
  };
}
```

## 4. User Profile Management

### 4.1 Profile Tabs (`pages/Profile.tsx`)

- **Basic Profile** (`profile/BasicProfile.tsx`):

  - Personal information (name, email, phone)
  - Avatar upload with cropping
  - Password management (change/reset)
  - Account deletion
  - Biometric settings

- **Business Profile** (`profile/BusinessProfile.tsx`):

  - Business information (name, headline, pitch)
  - Service tags management
  - Rich text editor for business pitch
  - Dynamic tag creation

- **Customize Profile** (`profile/CustomizeProfile.tsx`):

  - Personal branding colors
  - Font family selection
  - Logo and banner upload
  - Real-time preview

- **Notification Settings**:
  - Granular notification preferences
  - Email/push notification toggles
  - Meeting and activity notifications

### 4.2 Image Management

- **Avatar Upload**: Profile pictures with automatic cropping
- **Business Assets**: Logo and banner upload with optimization
- **Image Cropping**: Built-in cropping tool for consistent sizing
- **File Validation**: Size and format restrictions

### 4.3 Personal Branding

```typescript
interface PersonalBranding {
  primaryColor: string; // #hex format
  secondaryColor: string;
  backgroundColor: string;
  fontFamily: string; // Google Fonts integration
  logoUrl?: string;
  bannerUrl?: string;
}
```

## 5. Group Management & White-labeling

### 5.1 Group Settings (`groups/GroupSettingsForm.tsx`)

- **Basic Information**:

  - Group name and auto-generated slug
  - Review frequency configuration
  - Default meeting duration
  - Next review cycle dates

- **White-label Branding**:

  - Custom color schemes with theme presets
  - Logo, banner, favicon uploads
  - Brand logo for headers
  - Real-time theme preview

- **Advanced Branding**:
  - Primary, secondary, accent colors
  - Background, muted, card colors
  - Border and destructive colors
  - Typography customization

### 5.2 Group Member Management (`groups/GroupMemberList.tsx`)

- **Member Directory**:

  - Searchable and filterable member list
  - Sortable by name, email, join date, role, status
  - Mobile-responsive card view
  - Desktop table view with pagination

- **Role Management**:

  - Admin, Manager, User role assignment
  - Status management (Active, Pending, Inactive)
  - Bulk operations support
  - Permission-based access control

- **Member Actions**:
  - WhatsApp integration for direct contact
  - Member profile modals
  - Role and status change dropdowns
  - Activity tracking

### 5.3 White-labeling Features

```typescript
interface GroupBranding {
  // Colors
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  accentColor: string;
  mutedColor: string;
  cardColor: string;
  borderColor: string;
  destructiveColor: string;

  // Assets
  logoUrl?: string; // Group logo
  bannerUrl?: string; // Group banner
  faviconUrl?: string; // Custom favicon
  brandLogoUrl?: string; // Header brand logo

  // Analytics
  googleAnalyticsId?: string;
  metaPixelId?: string;
  googleTagManagerId?: string;
}
```

## 6. Theme System & Dynamic Branding

### 6.1 Theme Context (`contexts/ThemeContext.tsx`)

- **Multi-theme Support**:
  - Light/Dark system themes
  - Group-specific custom themes
  - Real-time theme switching
  - Persistent theme preferences

### 6.2 Dynamic Brand Component (`components/DynamicBrand.tsx`)

- **Adaptive Branding**:
  - Shows group logo when available
  - Falls back to group name
  - Default "ConnectPro" branding
  - Responsive sizing (small, medium, large)
  - Error handling for failed logo loads

### 6.3 Theme Implementation

```typescript
interface ThemeSystem {
  systemThemes: ["light", "dark"];
  groupTheme?: GroupBranding;
  themePresets: ThemePreset[];
  dynamicBranding: {
    logoFallback: string;
    brandName: string;
    adaptiveColors: boolean;
  };
}
```

## 7. Multi-language Support

### 7.1 Internationalization

- **Language Support**: English and Portuguese (Brazil)
- **Dynamic Translation**: Real-time language switching
- **Localized Content**: All UI elements and messages
- **Regional Formatting**: Dates, numbers, and currencies

### 7.2 Language Switcher (`components/LanguageSwitcher.tsx`)

- **Easy Toggle**: Quick language selection
- **Persistent Preference**: Remembers user choice
- **Context-aware**: Adapts to group preferences

## 8. Mobile-Responsive Design

### 8.1 Responsive Components

- **Mobile-first Approach**: All components work on mobile
- **Adaptive Layouts**: Card views for mobile, tables for desktop
- **Touch-friendly**: Large tap targets and gestures
- **Keyboard Optimization**: Smooth scrolling and focus management

### 8.2 Mobile-specific Features

- **Biometric Authentication**: Fingerprint/Face ID support
- **Push Notifications**: Mobile app integration
- **Offline Support**: Basic functionality without internet
- **App-like Experience**: PWA capabilities

## 9. Implementation Priority for Laravel Backend

### Phase 1A - Core Authentication

1. OTP-based email authentication system
2. User registration and email verification
3. Session management without passwords
4. Basic user profile management

### Phase 1B - Admin Panel

1. Super admin dashboard
2. User management (create, search, impersonate)
3. Group creation and management
4. Basic role-based access control

### Phase 1C - Group Features

1. Group dashboard with member list
2. Basic group settings and branding
3. Member role management
4. Group-specific themes

### Phase 1D - Advanced Features

1. Full white-labeling system
2. Advanced theme customization
3. Image upload and management
4. Multi-language support

## 10. Technical Requirements

### 10.1 Laravel Backend APIs

```php
// Authentication
POST /api/auth/login-otp        // Send OTP email
POST /api/auth/verify-otp       // Verify OTP token
POST /api/auth/logout           // Logout user

// User Management
GET  /api/admin/users           // List users with pagination
POST /api/admin/users           // Create new user
POST /api/admin/users/{id}/impersonate // Impersonate user
DELETE /api/admin/users/{id}    // Delete user

// Group Management
GET  /api/groups                // List user's groups
POST /api/groups                // Create new group
PUT  /api/groups/{id}/settings  // Update group settings
PUT  /api/groups/{id}/branding  // Update group branding

// Profile Management
GET  /api/profile               // Get user profile
PUT  /api/profile               // Update profile
POST /api/profile/avatar        // Upload avatar
PUT  /api/profile/branding      // Update personal branding
```

### 10.2 Database Models

```php
// Key models needed
User                 // Extended with branding fields
Group                // With white-labeling support
UserGroupRole        // Pivot with role management
GroupBranding        // Separate branding configuration
EmailVerification    // OTP token management
BiometricToken       // Mobile authentication tokens
```

### 10.3 File Storage

- **Avatar/Logo Storage**: Secure file upload and serving
- **Image Processing**: Automatic resizing and optimization
- **CDN Integration**: Fast image delivery
- **File Validation**: Security and format checking

This comprehensive UI feature extraction provides a roadmap for implementing a modern, white-label multi-tenant application with sophisticated user and group management capabilities, all while maintaining the clean, responsive design of the original frontend.
