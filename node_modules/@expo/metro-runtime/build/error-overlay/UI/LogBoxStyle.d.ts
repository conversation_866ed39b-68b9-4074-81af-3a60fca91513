/**
 * Copyright (c) 650 Industries.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export declare function getBackgroundColor(opacity?: number): string;
export declare function getBackgroundLightColor(opacity?: number): string;
export declare function getBackgroundDarkColor(opacity?: number): string;
export declare function getWarningColor(opacity?: number): string;
export declare function getWarningDarkColor(opacity?: number): string;
export declare function getFatalColor(opacity?: number): string;
export declare function getFatalDarkColor(opacity?: number): string;
export declare function getErrorColor(opacity?: number): string;
export declare function getErrorDarkColor(opacity?: number): string;
export declare function getLogColor(opacity?: number): string;
export declare function getWarningHighlightColor(opacity?: number): string;
export declare function getDividerColor(opacity?: number): string;
export declare function getHighlightColor(opacity?: number): string;
export declare function getTextColor(opacity?: number): string;
//# sourceMappingURL=LogBoxStyle.d.ts.map